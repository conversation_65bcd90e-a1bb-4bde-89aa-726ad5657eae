const WebSocket = require('ws');

const wss = new WebSocket.Server({ port: 8080 });

console.log('WebSocket server started on port 8080');

wss.on('connection', function connection(ws) {
  console.log('Client connected');

  const welcomeMessage = {
    messageType: 2,
    data: 'Welcome to the WebSocket server!'
  };
  ws.send(JSON.stringify(welcomeMessage));

  ws.on('message', function incoming(message) {
    console.log('Server received message from client:', message.toString());
    
    const response = {
      messageType: 2,
      data: message.toString() + ' from server'
    };
    ws.send(JSON.stringify(response));
  });

  // 心跳设置
  const interval = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      const periodicMessage = {
        messageType: 1,
        timestamp: new Date().toISOString(),
        data: Math.random() * 100
      };
      ws.send(JSON.stringify(periodicMessage));
    }
  }, 5000);

  ws.on('close', function close() {
    console.log('Client disconnected');
    clearInterval(interval);
  });
});
