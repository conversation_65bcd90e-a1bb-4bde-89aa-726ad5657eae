import React from "react";
import { Popup } from "antd-mobile";
function PortalRealTest() {
  function stop (e) {
    // e.stopPropagation()
    console.log('common 被点击了');
  }
  document.body.addEventListener('click', (e) => {
    console.log('body被点击了 冒泡', e);
  })
  // document.body.addEventListener('click', (e) => {
  //   console.log('body被点击了 捕获', e);
  // }, true)
  return (
    <div onClick={e => console.log('parent 被点击了', e)}>
      <div onClick={stop} style={{position: 'absolute', zIndex: 9999, top: '200px'}}>outside popup</div>
      <Popup visible={true} >
        <div onClick={stop}>inside popup</div>
      </Popup>
    </div>
  )
}

export default PortalRealTest;
