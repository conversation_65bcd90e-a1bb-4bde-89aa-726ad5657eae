import React from "react";
function EventOrder() {
  document.body.addEventListener('click', () => {
    console.log('body被点击了 捕获');
  }, true)
  document.getElementById('root').addEventListener('click', () => {
    console.log('root被点击了 捕获');
  }, true)
  document.body.addEventListener('click', () => {
    console.log('body被点击了 冒泡');
  })
  document.getElementById('root').addEventListener('click', () => {
    console.log('root被点击了 冒泡');
  })
  return (
    <div>
      <h1>Event Order</h1>
      <button>
        Click me
      </button>
    </div>
  );
}

export default EventOrder;
