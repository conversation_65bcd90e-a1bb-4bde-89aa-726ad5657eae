import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import Portal from "./components/Portal";
import Login from "./components/Login";
import Layout from "./components/Layout";
import { Provider } from "react-redux";
import store from "./store/index";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";

function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div style={{ padding: '20px' }}>
          <h1>Redux Saga WebSocket Test</h1>

          {/* 临时显示组件来测试 */}
          <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
            <h2>Counter Component Test:</h2>
            <Counter />
          </div>

          <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
            <h2>WebSocket Test Component:</h2>
            <WebSocketTest />
          </div>

          <hr />
          <h2>Router Test:</h2>

          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<Navigate to="login" replace />} />
              <Route path="login" element={<Login />} />
            </Route>
            <Route path="app">
              <Route index element={<Counter />} />
              <Route path="portal" element={<Portal />} />
            </Route>
          </Routes>
        </div>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
