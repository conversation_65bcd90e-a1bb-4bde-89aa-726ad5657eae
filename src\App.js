import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import Portal from "./components/Portal";
import EventOrder from "./components/EventOrder";
import PortalRealTest from "./components/PortalRealTest";
import { Provider } from "react-redux";
import store from "./store/index";
import React from "react";
import routes from "./routes";
import { useRoutes, BrowserRouter } from "react-router-dom";

// 创建一个内部组件来使用 useRoutes
function AppRoutes() {
  const element = useRoutes(routes);
  return element;
}

function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div style={{ padding: '20px' }}>
          {/* <Counter /> */}
          {/* <Portal /> */}
          {/* <EventOrder/> */}
          {/* <WebSocketTest /> */}
          {/* <PortalRealTest/> */}
          <AppRoutes />
        </div>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
