import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import Portal from "./components/Portal";
import EventOrder from "./components/EventOrder";
import PortalRealTest from "./components/PortalRealTest";
import { Provider } from "react-redux";
import store from "./store/index";
import React from "react";

function App() {
  return (
    <Provider store={store}>
      <div style={{ padding: '20px' }}>
        <Counter />
        {/* <Portal /> */}
        {/* <EventOrder/> */}
        {/* <WebSocketTsest /> */}
        {/* <PortalRealTest/> */}
      </div>
    </Provider>
  );
}

export default App;
