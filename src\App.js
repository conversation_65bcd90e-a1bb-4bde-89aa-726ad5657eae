import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import { Provider } from "react-redux";
import store from "./store/index";
import React from "react";
import routes from "./routes";
import { useRoutes, BrowserRouter } from "react-router-dom";


function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div style={{ padding: '20px' }}>
          {useRoutes(routes)}
        </div>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
