import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import { Provider } from "react-redux";
import store from "./store/index";
import { BrowserRouter, useRoutes } from "react-router-dom";
import createRoutes from "./routes";

// 使用 useRoutes 钩子的组件
function AppRoutes() {
  // 调用函数获取路由配置，这样 JSX 元素在 Router 上下文中创建
  const routes = createRoutes();

  // 使用 useRoutes 钩子
  const element = useRoutes(routes);
  return element;
}

function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div style={{ padding: '20px' }}>
          {/* <AppRoutes /> */}
          {useRoutes(routes)}
        </div>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
