
// 自定义channel用到的消息type
// 作为收到服务器消息和执行存储操作的一个中介
export const WS_CONNECTED = 'WS_CONNECTED';
export const WS_DISCONNECTED = 'WS_DISCONNECTED';
export const WS_MESSAGE_RECEIVED = 'WS_MESSAGE_RECEIVED';
export const WS_ERROR = 'WS_ERROR';
export const WELCOME_MESSAGE = 'WELCOME_MESSAGE';
export const ECHO_RESPONSE = 'ECHO_RESPONSE';
export const PERIODIC_UPDATE = 'PERIODIC_UPDATE';




// 不同的ws的消息有不同的类型
// 真正去dispatch用到的action的type
export const MessageType = {
  HEARTBEAT: 'HEARTBEAT',
  REALTIME_DATA: 'REALTIME_DATA',
}

// 服务端消息和store消息的映射关系
export const ActionTypeMap = {
  1: MessageType.HEARTBEAT,
  2: MessageType.REALTIME_DATA
}
  







