const path = require("path");
const htmlWebpackPlugin = require("html-webpack-plugin");

module.exports = {
  mode: "development",
  devtool: "eval-source-map",
  entry: {
    main: "./src/index.js",
    // debug: "./src/debug-redux.js",
  },
  output: {
    path: path.resolve(__dirname, "build"),
    filename: "[name].js",
    clean: true, // 清理输出目录
    publicPath: "/", // 确保资源从根路径加载
  },
  resolve: {
    extensions: ['.js', '.jsx', '.json'], // 支持这些文件扩展名
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/, // 同时处理 .js 和 .jsx 文件
        exclude: /node_modules\/(?!(@reduxjs\/toolkit)\/).*/, // 排除 node_modules，但包含 @reduxjs/toolkit
        use: [
          {
            loader: "babel-loader",
            options: {
              presets: [
                ["@babel/preset-env", {
                  targets: {
                    browsers: ["last 2 versions", "ie >= 11"]
                  },
                  modules: false, // 让 Webpack 处理模块
                }],
                ["@babel/preset-react", {
                  runtime: "automatic", // 使用新的 JSX 转换（React 17+）
                  development: true, // 开发模式下启用更好的调试
                }]
              ],
              plugins: [
                "@babel/plugin-syntax-dynamic-import", // 支持动态导入
              ],
              cacheDirectory: true, // 启用缓存以提高构建速度
            },
          },
        ],
      },
      {
        test: /\.css$/,
        use: [
          "style-loader",
          "css-loader",
        ],
      },
      {
        test: /\.(js|jsx)$/,
        include: /node_modules\/@reduxjs\/toolkit/,
        use: ["source-map-loader"],
        enforce: "pre",
      },
    ],
  },
  plugins: [
    new htmlWebpackPlugin({
      template: "./public/index.html",
      chunks: ["main"],
      filename: "index.html",
      inject: true, // 自动注入脚本
    }),
    // new htmlWebpackPlugin({
    //   template: "./public/index.html",
    //   chunks: ["debug"],
    //   filename: "debug.html",
    // }),
  ],
  devServer: {
    host: "127.0.0.1",
    port: 8081,
    hot: true, // 启用热模块替换
    open: true, // 自动打开浏览器
    historyApiFallback: {
      // 支持 React Router，但排除静态资源
      disableDotRule: true,
      rewrites: [
        { from: /^\/(?!.*\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)).*$/, to: '/index.html' }
      ]
    },
    static: {
      directory: path.join(__dirname, 'public'),
      publicPath: '/',
    },
    allowedHosts: [
      "localhost",
      "127.0.0.1",
      ".test", // 允许所有 .test 结尾的域名
    ],
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers":
        "X-Requested-With, content-type, Authorization",
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
  },
};
