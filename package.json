{"name": "redux-test", "version": "1.0.0", "description": "saga里面有几种类型", "main": "webpack.config.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "webpack server --config ./webpack.config.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/preset-env": "^7.27.1", "@babel/preset-react": "^7.27.1", "@reduxjs/toolkit": "^2.8.1", "antd-mobile": "^5.39.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "mqtt": "^5.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "source-map-loader": "^5.0.0", "style-loader": "^4.0.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}