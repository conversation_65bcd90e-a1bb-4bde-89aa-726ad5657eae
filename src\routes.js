import Counter from "./components/Counter";
import Portal from "./components/Portal";
import Login from "./components/Login";
import Layout from "./components/Layout";
import { Navigate } from "react-router-dom";


const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true, element: <Navigate to='login'/>,
      },
      {
        path: 'login',
        element: <Login/>
      }
    ]
  },
  {
    path: 'app',
    children: [
      {
        index: true,
        element: <Counter/>,
      },
      {
        path: 'portal',
        element: <Portal/>
      }
    ]
  }
];

export default routes;