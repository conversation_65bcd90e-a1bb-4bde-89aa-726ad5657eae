import Counter from "./components/Counter";
import Portal from "./components/Portal";
import Login from "./components/Login";
import Layout from "./components/Layout";
import { Navigate } from "react-router-dom";

// 导出一个函数，该函数返回路由配置
// 这样 JSX 元素会在组件渲染时创建，而不是在模块加载时
const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      // {
      //   index: true,
      //   element: <Navigate to="login" replace />,
      // },
      {
        path: 'login',
        element: <Login />
      }
    ]
  },
  {
    path: 'app',
    children: [
      {
        index: true,
        element: <Counter />,
      },
      {
        path: 'portal',
        element: <Portal />
      }
    ]
  }
];

export default routes;