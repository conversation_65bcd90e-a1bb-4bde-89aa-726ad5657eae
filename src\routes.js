import Counter from "./components/Counter";
import WebSocketTest from "./components/WebSocketTest";
import Portal from "./components/Portal";
import EventOrder from "./components/EventOrder";
import PortalRealTest from "./components/PortalRealTest";
import Login from "./components/Login";
import Layout from "./components/Layout";
import { Navigate } from "react-router-dom";


const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true, element: <Navigate to='login'/>,
      },
      {
        path: 'login',
        element: <Login/>
      }
    ]
  },
  {
    path: 'main',
    children: [
      {
        index: true,
        element: <Counter/>,
      },
      {
        path: 'portal',
        element: <PortalRealTest/>
      }
    ]
  }
];

export default routes;