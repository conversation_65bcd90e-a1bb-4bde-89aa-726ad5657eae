import { eventChannel, buffers } from "redux-saga";
import { all, take, put, call } from "redux-saga/effects";
import {
  WS_CONNECTED,
  WS_DISCONNECTED,
  WS_MESSAGE_RECEIVED,
  MessageType,
  ActionTypeMap
} from './action/websocket';


let wsChannel;
let wsClient;


function createWsConnection() {
  // 下面返回一个增强版的channel对象
  return eventChannel((emitter) => {
    const createWS = () => {
      wsClient = new WebSocket('ws://localhost:8080')
      wsClient.onopen = () => {
        console.log('ws client connected')
        emitter({type: WS_CONNECTED, data: ''})
      }
      wsClient.onmessage = (event) => {
        // 浏览器这边收到的数据就是一个进过websocket处理的MessageEvent对象
        // 服务器的数据存在data属性里面
        console.log('ws client received data: ', event.data)
        const data = JSON.parse(event.data)
        emitter({type: WS_MESSAGE_RECEIVED, data})
      }
      wsClient.onclose = () => {
        console.log('ws client closed')
        emitter({type: WS_DISCONNECTED, data: ''})
        // 注意这里为什么要加上return？？？
        // 在哪里会拿到这个timer
        return setTimeout(() => {
          createWS()
        }, 1000)
      }
    }
    createWS()
    window.wsClient = wsClient
    return () => {
      wsClient.close()
    }
  })
}


function* watchWsResponse() {
  while (true) {
    const emitData = yield take(wsChannel)
    // 收到的数据就是在createWs的时候，也就是自定义channel那边put的对象，比如：{type: WS_CONNECTED, data: ''}
    switch (emitData?.type) {
      // Q：这里为什么只需要处理messageReceived的情况？
      // A：一般只有在收到服务器传来的数据的时候，data才有意义，不然连接成功和断开连接传入的data属性是空值
      case WS_MESSAGE_RECEIVED:
        // 【这个时候收到的data数据最好是已经处理过的，已经成为一个对象的数据！！！】
        // 这个时候解构出type看是什么类型的数据，心跳还是实时有用的数据？？
        const { messageType } = emitData?.data
        const actionType = ActionTypeMap[messageType]
        yield put({
          type: actionType,
          payload: emitData?.data
        })
        break
      default:
        break
    }
  }
}




export default function* rootSaga() {
  wsChannel = createWsConnection()
  // debuggers
  yield all([watchWsResponse()])
}






