import { applyMiddleware, combineReducers, createStore, compose } from "redux";
import { configureStore } from "@reduxjs/toolkit";
import { counterReducer, websocketReducer } from "./reducers";
import createSagaMiddleware from "redux-saga";
import rootSaga from "./rootSaga_enhanceVersion";
const sagaMiddleware = createSagaMiddleware();
const AllReducers = combineReducers({
  counter: counterReducer,
  websocket: websocketReducer,
});

const store = configureStore({
  reducer: AllReducers,
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware({
      thunk: false,
    }).concat(sagaMiddleware)
})

sagaMiddleware.run(rootSaga);

export default store;
