import * as effectTypes from "./effectTypes";
import proc from "./proc";
import is from "./is";
import { createAllStyleChildCallbacks } from "./utils";

function runTakeEffect(env, payload, next) {
  // matcher函数的参数是action
  // payload的参数是next执行之后的value里面的（也就是take函数的返回值）的payload对象，里面的pattern保存了take函数参数里面的字符串
  // next就是总调度函数
  const matcher = (input) => input.type === payload.pattern;
  env.channel.take(next, matcher);
}

// 源码的runTakeEffect函数
function runTakeEffect(env, _ref3, cb) {
  // cb就是next函数
  // 注意：这里的channel用的是公共的channel或者effec对象的channel（这种情况下put就是只拿出数组的第一个函数执行）
  var _ref3$channel = _ref3.channel,
      channel = _ref3$channel === void 0 ? env.channel : _ref3$channel,
      pattern = _ref3.pattern,
      maybe = _ref3.maybe;

  var takeCb = function takeCb(input) {
    if (input instanceof Error) {
      cb(input, true);
      return;
    }

    if (isEnd(input) && !maybe) {
      cb(TERMINATE);
      return;
    }

    cb(input);
  };

  try {
    channel.take(takeCb, notUndef(pattern) ? matcher(pattern) : null);
  } catch (err) {
    cb(err, true);
    return;
  }

  cb.cancel = takeCb.cancel;
}




function runPutEffect(env, payload, next) {
  // 这里的dispatch就是（在src\redux-saga\middleware.js里面）
  // (action) => {
  //   channel.put(action);
  //   return next(action);
  // }
  // 注意，里面的next是别的中间件，不是迭代器的总控next函数
  // 相当于我把put写在了dispatch里面，这里直接调用它就好，不需要直接写put
  // put函数内部就是把所有的next函数拿出来执行，而take函数是把next放入队列中
  env.dispatch(payload.pattern);
  next();
}

// 源码的runPutEffect函数
var queue = [];
function runPutEffect(env, _ref2, cb) {
  var channel = _ref2.channel,
      action = _ref2.action,
      resolve = _ref2.resolve;

  // 绕了一圈，实际上是在执行里面的dispatch函数
  asap(function () {
    var result;

    try {
      result = (channel ? channel.put : env.dispatch)(action);
    } catch (error) {
      cb(error, true);
      return;
    }

    if (resolve && promise(result)) {
      resolvePromise(result, cb);
    } else {
      cb(result);
    }
  });
}
function asap(task) {
  queue.push(task);

  if (!semaphore) {
    suspend();
    flush();
  }
}
function suspend() {
  semaphore++;
}
function release() {
  semaphore--;
}
function flush() {
  release();
  var task;

  while (!semaphore && (task = queue.shift()) !== undefined) {
    exec(task);
  }
}
function exec(task) {
  try {
    suspend();
    task();
  } finally {
    release();
  }
}




function runForkEffect(env, payload, next) {
  // payload.fn是saga函数，saga函数等于迭代器函数(执行后得到的是迭代器对象)
  // next是总控函数
  // 这里是开启一个新的子线程，不会阻塞当前的saga，如何体现的？？
  const iterator = payload.fn();
  const task = proc(env, iterator);
  // 这个proc执行完之后，不会阻塞调用next，这里手动提前在外部调用next（put函数也一样）
  // 把任务对象给到外面的yield()前面的变量
  next(task);
}

// 源码的fork
function runForkEffect(env, _ref7, cb, _ref8) {
  var context = _ref7.context,
      fn = _ref7.fn,
      args = _ref7.args,
      detached = _ref7.detached;
  var parent = _ref8.task;
  var taskIterator = createTaskIterator({
    context: context,
    fn: fn,
    args: args
  });
  var meta = getIteratorMetaInfo(taskIterator, fn);
  immediately(function () {
    var child = proc(env, taskIterator, parent.context, current, meta, detached, undefined);

    if (detached) {
      cb(child);
    } else {
      // 如果proc返回的task对象里面有isRunning函数且结果为true
      // 执行外部的next函数，把当前的task传入作为参数
      if (child.isRunning()) {
        parent.queue.addTask(child);
        cb(child);
      } else if (child.isAborted()) {
        parent.queue.abort(child.error());
      } else {
        cb(child);
      }
    }
  }); // Fork effects are non cancellables
}




// call会阻塞saga函数的执行
function runCallEffect(env, payload, next) {
  const { fn, args } = payload;
  const result = fn(...args);
  
  // 这里源码还判断了result是不是一个iterator！！！
  if (is.promise(result)) {
    result.then(
      (res) => next(res),
      (err) => next(err, true)
    );
  } else {
    next(result);
  }
}

function runCpsEffect(env, payload, next) {
  const { fn, args } = payload;
  // 回调里面的逻辑是去执行next函数
  fn(...args, (err, res) => {
    if (err) {
      next(err, true);
    } else {
      next(res);
    }
  });
}

function runAllEffect(env, effects, next, { runEffect }) {
  // 这里的effects就是gen函数的数组
  if (effects.length === 0) {
    return next([]);
  }
  const keys = Object.keys(effects);
  const childCallbacks = createAllStyleChildCallbacks(effects, next);
  keys.forEach((key) => {
    // 这里为什么是用runEffect而不是用proc，每个effect不是一个iterator吗
    // 如果直接用proc的话，proc的env参数去哪里传递呢？
    runEffect(effects[key], childCallbacks[key]);
  });
}

function runCancelEffect(env, task, next) {
  task.cancel();
  next();
}

const effectRunnerMap = {
  [effectTypes.TAKE]: runTakeEffect,
  [effectTypes.PUT]: runPutEffect,
  [effectTypes.FORK]: runForkEffect,
  [effectTypes.CALL]: runCallEffect,
  [effectTypes.CPS]: runCpsEffect,
  [effectTypes.ALL]: runAllEffect,
  [effectTypes.CANCEL]: runCancelEffect,
};

export default effectRunnerMap;
