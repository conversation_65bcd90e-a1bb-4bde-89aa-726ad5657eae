import { MessageType } from '../action/websocket';
import { createSlice } from '@reduxjs/toolkit';
const initialState = {
  connected: false,
  messages: [],
  lastMessage: null,
  error: null
};



const websocket = createSlice({
  name: 'websocket',
  initialState: {
    connected: false,
    messages: [],
  },
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(MessageType.HEARTBEAT, (state, action) => {
      // Q：这里为什么可以直接改state的值，难道是因为toolkit内部实现了首先新建一个state，然后传进来吗
      // A：因为toolkit内部在执行这个函数之前创造了一个proxy对象，这里的state就是proxy对象
      state.connected = true
    }),
    builder.addCase(MessageType.REALTIME_DATA, (state, action) => {
      state.messages.push(action.payload)
    })
  }
})

export default websocket.reducer;
